#!/bin/bash

# 終極修復腳本 - 完全解決SSH依賴問題
LOG_FILE="/var/log/n8n_ultimate_fix.log"

log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

log_message "Starting ultimate N8N SSH independence fix..."

# 1. 停止所有相關服務
log_message "Stopping all N8N related services..."
sudo systemctl stop n8n-independent.service 2>/dev/null
sudo systemctl disable n8n-independent.service 2>/dev/null
cd /root/n8n
docker-compose down 2>/dev/null

# 2. 殺死所有守護進程
log_message "Killing all daemon processes..."
sudo pkill -f "n8n_daemon.sh" 2>/dev/null
sudo rm -f /var/run/n8n_daemon.pid 2>/dev/null
sudo rm -f /var/run/n8n-service.lock 2>/dev/null

# 3. 使用systemd-run創建完全隔離的服務
log_message "Creating systemd-run isolated service..."

# 創建啟動腳本
cat > /tmp/n8n_isolated_start.sh << 'EOF'
#!/bin/bash
cd /root/n8n
export PATH="/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"
export HOME="/root"

# 啟動Docker Compose
/usr/bin/docker-compose up -d

# 等待服務啟動
sleep 15

# 持續監控和重啟
while true; do
    # 檢查容器狀態
    if ! docker ps | grep -q "n8n_n8n_1.*Up"; then
        echo "$(date) - N8N container not running, restarting..." >> /var/log/n8n_isolated.log
        docker-compose down 2>/dev/null
        sleep 5
        docker-compose up -d 2>/dev/null
        sleep 15
    fi
    
    # 檢查HTTP響應
    if ! wget --quiet --tries=1 --timeout=5 --spider http://localhost:5678/ 2>/dev/null; then
        echo "$(date) - N8N HTTP not responding, restarting container..." >> /var/log/n8n_isolated.log
        docker restart n8n_n8n_1 2>/dev/null
        sleep 10
    fi
    
    sleep 60
done
EOF

chmod +x /tmp/n8n_isolated_start.sh

# 4. 使用systemd-run啟動完全隔離的服務
log_message "Starting isolated N8N service with systemd-run..."
sudo systemd-run \
    --unit=n8n-isolated \
    --description="N8N Isolated Service" \
    --remain-after-exit \
    --setenv=PATH="/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin" \
    --setenv=HOME="/root" \
    --working-directory=/root/n8n \
    /tmp/n8n_isolated_start.sh

# 5. 等待服務啟動
log_message "Waiting for service to start..."
sleep 20

# 6. 驗證服務狀態
if docker ps | grep -q "n8n_n8n_1.*Up"; then
    log_message "SUCCESS: N8N is running in isolated mode"
    
    if wget --quiet --tries=1 --timeout=5 --spider http://localhost:5678/ 2>/dev/null; then
        log_message "SUCCESS: N8N HTTP endpoint is accessible"
    else
        log_message "WARNING: N8N may still be starting up"
    fi
else
    log_message "ERROR: N8N failed to start in isolated mode"
fi

# 7. 創建管理命令
cat > /usr/local/bin/n8n-manage << 'EOF'
#!/bin/bash
case "$1" in
    status)
        systemctl status n8n-isolated.service --no-pager
        echo ""
        docker ps | grep n8n
        ;;
    restart)
        sudo systemctl restart n8n-isolated.service
        ;;
    stop)
        sudo systemctl stop n8n-isolated.service
        cd /root/n8n && docker-compose down
        ;;
    logs)
        tail -f /var/log/n8n_isolated.log
        ;;
    *)
        echo "Usage: $0 {status|restart|stop|logs}"
        ;;
esac
EOF

chmod +x /usr/local/bin/n8n-manage

log_message "Ultimate fix completed. Use 'n8n-manage status' to check service."
log_message "Service should now be completely independent of SSH sessions."
