#!/bin/bash

# N8N 守護進程腳本 - 確保服務完全獨立於SSH會話
LOG_FILE="/var/log/n8n_daemon.log"
PID_FILE="/var/run/n8n_daemon.pid"
N8N_DIR="/root/n8n"

log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# 檢查是否已經在運行
check_daemon_running() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            log_message "Daemon already running with PID $pid"
            exit 1
        else
            rm -f "$PID_FILE"
        fi
    fi
}

# 啟動守護進程
start_daemon() {
    check_daemon_running
    
    log_message "Starting N8N daemon..."
    
    # 使用nohup和setsid確保完全脫離終端
    setsid nohup bash -c '
        echo $$ > '"$PID_FILE"'
        
        while true; do
            # 檢查Docker服務
            if ! systemctl is-active --quiet docker; then
                echo "$(date) - Docker service not active, starting..." >> '"$LOG_FILE"'
                systemctl start docker
                sleep 10
            fi
            
            # 檢查N8N容器
            cd '"$N8N_DIR"'
            N8N_STATUS=$(docker inspect --format="{{.State.Status}}" n8n_n8n_1 2>/dev/null)
            PG_STATUS=$(docker inspect --format="{{.State.Status}}" n8n_postgresql_1 2>/dev/null)
            
            if [ "$N8N_STATUS" != "running" ] || [ "$PG_STATUS" != "running" ]; then
                echo "$(date) - Containers not running, restarting..." >> '"$LOG_FILE"'
                docker-compose down 2>/dev/null
                sleep 5
                docker-compose up -d 2>/dev/null
                sleep 15
            fi
            
            # 檢查HTTP可訪問性
            if ! wget --quiet --tries=1 --timeout=5 --spider http://localhost:5678/ 2>/dev/null; then
                echo "$(date) - HTTP not accessible, restarting N8N container..." >> '"$LOG_FILE"'
                docker restart n8n_n8n_1 2>/dev/null
                sleep 10
            fi
            
            sleep 60  # 每分鐘檢查一次
        done
    ' > /dev/null 2>&1 &
    
    local daemon_pid=$!
    echo "$daemon_pid" > "$PID_FILE"
    log_message "Daemon started with PID $daemon_pid"
}

# 停止守護進程
stop_daemon() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            kill "$pid"
            rm -f "$PID_FILE"
            log_message "Daemon stopped (PID $pid)"
        else
            log_message "Daemon not running"
            rm -f "$PID_FILE"
        fi
    else
        log_message "No PID file found"
    fi
}

# 檢查守護進程狀態
status_daemon() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            log_message "Daemon is running with PID $pid"
        else
            log_message "Daemon PID file exists but process not running"
            rm -f "$PID_FILE"
        fi
    else
        log_message "Daemon is not running"
    fi
}

case "$1" in
    start)
        start_daemon
        ;;
    stop)
        stop_daemon
        ;;
    restart)
        stop_daemon
        sleep 2
        start_daemon
        ;;
    status)
        status_daemon
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|status}"
        exit 1
        ;;
esac
