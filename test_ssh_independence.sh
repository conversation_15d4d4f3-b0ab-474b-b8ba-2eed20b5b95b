#!/bin/bash

# SSH會話獨立性測試腳本
LOG_FILE="/var/log/n8n_ssh_test.log"

log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# 測試N8N服務在不同情況下的狀態
test_n8n_status() {
    local test_name="$1"
    log_message "=== Testing: $test_name ==="
    
    # 檢查容器狀態
    N8N_STATUS=$(docker inspect --format='{{.State.Status}}' n8n_n8n_1 2>/dev/null)
    PG_STATUS=$(docker inspect --format='{{.State.Status}}' n8n_postgresql_1 2>/dev/null)
    
    log_message "Container Status - N8N: $N8N_STATUS, PostgreSQL: $PG_STATUS"
    
    # 檢查HTTP響應
    if wget --quiet --tries=1 --timeout=5 --spider http://localhost:5678/ 2>/dev/null; then
        log_message "HTTP Status: ACCESSIBLE"
    else
        log_message "HTTP Status: NOT ACCESSIBLE"
    fi
    
    # 檢查進程
    N8N_PROCESSES=$(ps aux | grep -c "[n]ode.*n8n")
    log_message "N8N Processes: $N8N_PROCESSES"
    
    # 檢查SSH會話
    SSH_SESSIONS=$(who | grep -c "pts")
    log_message "Active SSH Sessions: $SSH_SESSIONS"
    
    log_message "=== End Test: $test_name ==="
    echo ""
}

# 主測試流程
main() {
    log_message "Starting SSH Independence Test"
    
    # 測試當前狀態
    test_n8n_status "Current State"
    
    # 模擬SSH斷開後的狀態檢查
    log_message "Simulating SSH disconnect scenario..."
    
    # 使用nohup確保進程獨立性
    nohup bash -c '
        sleep 30
        echo "$(date) - Testing after simulated SSH disconnect" >> /var/log/n8n_ssh_test.log
        /root/n8n/test_ssh_independence.sh check_only
    ' > /dev/null 2>&1 &
    
    log_message "Background test scheduled. Check log in 30 seconds."
}

# 僅檢查模式
if [ "$1" = "check_only" ]; then
    test_n8n_status "After SSH Disconnect Simulation"
else
    main
fi
