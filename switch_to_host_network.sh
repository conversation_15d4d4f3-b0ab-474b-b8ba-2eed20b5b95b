#!/bin/bash

LOG_FILE="/var/log/n8n_switch.log"

log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

cd /root/n8n

log_message "Switching N8N to host network mode..."

# 停止當前服務
log_message "Stopping current N8N services..."
docker-compose down

# 備份當前配置
log_message "Backing up current configuration..."
cp docker-compose.yml docker-compose-bridge.yml.backup

# 使用host網絡配置
log_message "Switching to host network configuration..."
cp docker-compose-host.yml docker-compose.yml

# 啟動新配置
log_message "Starting N8N with host network..."
docker-compose up -d

# 等待服務啟動
sleep 15

# 檢查服務狀態
if docker-compose ps | grep -q "Up"; then
    log_message "SUCCESS: N8N switched to host network mode"
    
    # 測試連接
    if wget --quiet --tries=1 --timeout=5 --spider http://localhost:5678/ 2>/dev/null; then
        log_message "SUCCESS: N8N is accessible on host network"
    else
        log_message "WARNING: N8N may not be fully ready yet"
    fi
else
    log_message "ERROR: Failed to start N8N with host network"
    
    # 回滾到原配置
    log_message "Rolling back to bridge network..."
    docker-compose down
    cp docker-compose-bridge.yml.backup docker-compose.yml
    docker-compose up -d
fi

log_message "Switch operation completed"
