#!/bin/bash

# 最終SSH獨立性測試
LOG_FILE="/var/log/n8n_final_test.log"

log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

test_n8n_accessibility() {
    local test_name="$1"
    log_message "=== Testing: $test_name ==="
    
    # 檢查容器狀態
    N8N_STATUS=$(docker inspect --format='{{.State.Status}}' n8n_n8n_1 2>/dev/null)
    N8N_HEALTH=$(docker inspect --format='{{.State.Health.Status}}' n8n_n8n_1 2>/dev/null)
    PG_STATUS=$(docker inspect --format='{{.State.Status}}' n8n_postgresql_1 2>/dev/null)
    PG_HEALTH=$(docker inspect --format='{{.State.Health.Status}}' n8n_postgresql_1 2>/dev/null)
    
    log_message "Container Status - N8N: $N8N_STATUS ($N8N_HEALTH), PostgreSQL: $PG_STATUS ($PG_HEALTH)"
    
    # 檢查端口監聽
    N8N_PORT=$(netstat -tln | grep ":5678 " | wc -l)
    PG_PORT=$(netstat -tln | grep ":5433 " | wc -l)
    log_message "Port Listening - N8N:5678: $N8N_PORT, PG:5433: $PG_PORT"
    
    # 檢查HTTP響應
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:5678/ | grep -q "200"; then
        log_message "HTTP Status: ACCESSIBLE (200 OK)"
        HTTP_OK=true
    else
        log_message "HTTP Status: NOT ACCESSIBLE"
        HTTP_OK=false
    fi
    
    # 檢查SSH會話
    SSH_SESSIONS=$(who | grep -c "pts" || echo "0")
    log_message "Active SSH Sessions: $SSH_SESSIONS"
    
    # 檢查進程所有者
    N8N_PROCESS_OWNER=$(ps aux | grep "[n]ode.*n8n" | awk '{print $1}' | head -1)
    log_message "N8N Process Owner: $N8N_PROCESS_OWNER"
    
    log_message "=== End Test: $test_name ==="
    echo ""
    
    return $HTTP_OK
}

# 主測試
main() {
    log_message "Starting Final SSH Independence Test"
    
    # 當前狀態測試
    test_n8n_accessibility "Current State with SSH Connected"
    
    # 創建後台測試任務
    log_message "Creating background test for SSH disconnect simulation..."
    
    # 使用at命令安排未來測試（如果可用）
    if command -v at >/dev/null 2>&1; then
        echo "/root/n8n/final_ssh_test.sh background_test" | at now + 2 minutes 2>/dev/null
        log_message "Background test scheduled for 2 minutes from now using 'at'"
    else
        # 使用nohup作為備選
        nohup bash -c '
            sleep 120
            /root/n8n/final_ssh_test.sh background_test
        ' >/dev/null 2>&1 &
        log_message "Background test scheduled for 2 minutes from now using 'nohup'"
    fi
    
    log_message "Test setup completed. Monitor log file: tail -f $LOG_FILE"
}

# 後台測試模式
background_test() {
    log_message "=== BACKGROUND TEST STARTED ==="
    test_n8n_accessibility "Background Test (Simulated SSH Disconnect)"
    log_message "=== BACKGROUND TEST COMPLETED ==="
}

# 根據參數執行不同模式
case "$1" in
    background_test)
        background_test
        ;;
    *)
        main
        ;;
esac
