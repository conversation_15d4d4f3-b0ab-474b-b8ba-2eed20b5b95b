#!/bin/bash

# N8N 服務監控腳本
LOG_FILE="/var/log/n8n_monitor.log"
N8N_DIR="/root/n8n"

# 記錄日誌函數
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | sudo tee -a "$LOG_FILE"
}

# 檢查N8N服務狀態
check_n8n_service() {
    cd "$N8N_DIR"

    # 檢查容器狀態
    N8N_STATUS=$(docker inspect --format='{{.State.Status}}' n8n_n8n_1 2>/dev/null)
    PG_STATUS=$(docker inspect --format='{{.State.Status}}' n8n_postgresql_1 2>/dev/null)

    if [ "$N8N_STATUS" != "running" ] || [ "$PG_STATUS" != "running" ]; then
        log_message "ERROR: N8N containers not running (N8N: $N8N_STATUS, PG: $PG_STATUS)"
        log_message "INFO: Attempting to restart N8N services via systemd"

        # 使用systemd重啟服務
        sudo systemctl restart n8n.service
        sleep 10

        # 檢查重啟結果
        N8N_STATUS_NEW=$(docker inspect --format='{{.State.Status}}' n8n_n8n_1 2>/dev/null)
        if [ "$N8N_STATUS_NEW" = "running" ]; then
            log_message "INFO: N8N services restarted successfully"
        else
            log_message "ERROR: Failed to restart N8N services"
        fi
    else
        # 檢查HTTP響應 (使用wget而不是curl)
        if ! wget --quiet --tries=1 --timeout=5 --spider http://localhost:5678/ 2>/dev/null; then
            log_message "WARNING: N8N HTTP endpoint not responding"
            log_message "INFO: Attempting to restart N8N container"
            docker restart n8n_n8n_1
            sleep 5
        fi
    fi
}

# 檢查系統時間同步
check_time_sync() {
    if ! timedatectl status | grep -q "System clock synchronized: yes"; then
        log_message "WARNING: System clock not synchronized"
        sudo timedatectl set-ntp true
        sudo systemctl restart systemd-timesyncd
        log_message "INFO: Time synchronization restarted"
    fi
}

# 檢查SSH會話影響
check_ssh_sessions() {
    SSH_COUNT=$(who | grep -c "pts")
    log_message "INFO: Active SSH sessions: $SSH_COUNT"

    # 如果沒有SSH會話但服務異常，記錄警告
    if [ "$SSH_COUNT" -eq 0 ]; then
        log_message "WARNING: No active SSH sessions detected"
    fi
}

# 主監控邏輯
main() {
    log_message "INFO: Starting N8N monitoring check"
    check_time_sync
    check_ssh_sessions
    check_n8n_service
    log_message "INFO: N8N monitoring check completed"
}

# 執行監控
main
