#!/bin/bash

# N8N 服務監控腳本
LOG_FILE="/var/log/n8n_monitor.log"
N8N_DIR="/root/n8n"

# 記錄日誌函數
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | sudo tee -a "$LOG_FILE"
}

# 檢查N8N服務狀態
check_n8n_service() {
    cd "$N8N_DIR"
    
    # 檢查容器狀態
    if ! docker-compose ps | grep -q "Up"; then
        log_message "ERROR: N8N containers are not running"
        log_message "INFO: Attempting to restart N8N services"
        
        # 嘗試重啟服務
        docker-compose down
        sleep 5
        docker-compose up -d
        
        if [ $? -eq 0 ]; then
            log_message "INFO: N8N services restarted successfully"
        else
            log_message "ERROR: Failed to restart N8N services"
        fi
    else
        # 檢查HTTP響應
        if ! curl -f -s http://localhost:5678/healthz > /dev/null 2>&1; then
            log_message "WARNING: N8N HTTP endpoint not responding"
            log_message "INFO: Attempting to restart N8N container"
            docker-compose restart n8n
        fi
    fi
}

# 檢查系統時間同步
check_time_sync() {
    if ! timedatectl status | grep -q "System clock synchronized: yes"; then
        log_message "WARNING: System clock not synchronized"
        sudo timedatectl set-ntp true
        sudo systemctl restart systemd-timesyncd
        log_message "INFO: Time synchronization restarted"
    fi
}

# 主監控邏輯
main() {
    log_message "INFO: Starting N8N monitoring check"
    check_time_sync
    check_n8n_service
    log_message "INFO: N8N monitoring check completed"
}

# 執行監控
main
